import React, { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/axios';
import { useSocket } from '@/context/SocketContext';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronDown, ChevronUp, Mail, Home, Calendar, UserCircle, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import RoleBadge from './RoleBadge';
import { getProfilePhotoUrl } from '@/utils/imageUtils';

interface Member {
  _id: string;
  username: string;
  fullName: string;
  email: string;
  propertyAddress: string;
  profilePhoto: string;
  role: string;
  isApproved: boolean;
  dueStatus: 'paid' | 'pending' | 'overdue';
  createdAt: string;
  isOnline: boolean;
  communityId?: string | {
    _id: string;
    name: string;
    streetAddress?: string;
  } | Array<{
    _id: string;
    name: string;
    streetAddress?: string;
  }>;
}

const LoadingRow = () => (
  <TableRow>
    <TableCell>
      <div className="flex items-center gap-3">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div>
          <Skeleton className="h-4 w-[150px]" />
          <Skeleton className="h-3 w-[120px] mt-2" />
        </div>
      </div>
    </TableCell>
    <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
    <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
    <TableCell><Skeleton className="h-4 w-[60px]" /></TableCell>
    <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
    <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
    <TableCell><Skeleton className="h-4 w-[24px]" /></TableCell>
  </TableRow>
);

const MemberDetails = () => {
  const [expandedMember, setExpandedMember] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const queryClient = useQueryClient();
  const { socket } = useSocket();

  // Community selection state (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    return localStorage.getItem('selectedCommunityId') || null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(false);
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userCommunityId = user?.communityId;
  const isCompanyAdmin = user.role === 'company_admin';
  const isHOAAdmin = user.role === 'admin';
  const isRegularUser = user.role === 'member';

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

    if (storedCommunityIds) {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else if (storedCommunityId) {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;
      console.log('MemberDetails: Received community selection change:', { communityId, communityIds, availableCommunities });

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
      }

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('MemberDetails: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection
        setSelectedCommunityId(null);
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  const { data: membersData, isLoading, isError } = useQuery({
    queryKey: ['members', selectedCommunityId, selectedCommunityIds],
    queryFn: async () => {
      // Build endpoint with community filtering
      let endpoint = '/api/members/approved';
      const params = [];

      // Role-based filtering using communityId (same as other components)
      if (isCompanyAdmin) {
        // Company admin can see all, but apply community filters if specified
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities")
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('MemberDetails: Company admin fetching members for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          // Single community selected
          params.push(`communityId=${selectedCommunityId}`);
          console.log('MemberDetails: Company admin fetching members for single community:', selectedCommunityId);
        }
        // If no filters, company admin sees all members (this is intentional)
      } else if (isRegularUser) {
        // Regular users (members) can only see members from their own street/community
        // They cannot change community selection - always restricted to their own community
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('MemberDetails: Regular user fetching members for their street community:', userCommunityId);
        } else {
          console.warn('MemberDetails: Regular user has no community assigned');
        }
      } else {
        // HOA Admins see members from communities they have access to
        if (selectedCommunityIds.length > 0) {
          // Multiple communities selected (including "All Communities" = all accessible communities)
          selectedCommunityIds.forEach(id => params.push(`communityId=${id}`));
          console.log('MemberDetails: HOA Admin fetching members for communities:', selectedCommunityIds);
        } else if (selectedCommunityId) {
          params.push(`communityId=${selectedCommunityId}`);
          console.log('MemberDetails: HOA Admin fetching members for single community:', selectedCommunityId);
        } else if (userCommunityId) {
          // Default to their community if no specific selection
          params.push(`communityId=${userCommunityId}`);
          console.log('MemberDetails: HOA Admin fetching members for their default community:', userCommunityId);
        }
      }

      // IMPORTANT: Always ensure we have some filtering for non-company-admin users
      if (!isCompanyAdmin && params.length === 0) {
        console.warn('MemberDetails: No filtering applied for non-company-admin user, applying default community filter');
        // Apply default user-based filtering with communityId only
        if (userCommunityId) {
          params.push(`communityId=${userCommunityId}`);
          console.log('MemberDetails: Applied default community filter for security:', userCommunityId);
        }
      }

      // For regular users, ensure they can never bypass their community restriction
      if (isRegularUser && userCommunityId && !params.some(p => p.includes(`communityId=${userCommunityId}`))) {
        console.warn('MemberDetails: Regular user attempting to access members outside their community - enforcing restriction');
        params = [`communityId=${userCommunityId}`]; // Override any other parameters
      }

      if (params.length > 0) {
        endpoint += `?${params.join('&')}`;
      }

      console.log('MemberDetails: Fetching members from endpoint:', endpoint);

      const res = await api.get(endpoint);
      return res.data;
    }
  });

  const allMembers = membersData?.users || [];

  // Filter members based on search term and active tab
  const filteredMembers = allMembers.filter((member: Member) => {
    // Hide company admins from member lists (they shouldn't be visible to HOA admins/members)
    if (member.role === 'company_admin') {
      return false;
    }

    const matchesSearch =
      searchTerm === '' ||
      member.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTab =
      activeTab === 'all' ||
      (activeTab === 'admin' && member.role === 'admin') ||
      (activeTab === 'resident' && member.role === 'member') ||
      (activeTab === 'property_manager' && member.role === 'property_manager') ||
      (activeTab === 'online' && member.isOnline);

    return matchesSearch && matchesTab;
  });

  // Listen for user status changes
  useEffect(() => {
    if (!socket) return;

    socket.on('userStatusChange', ({ userId, isOnline }) => {
      queryClient.setQueryData(['members'], (oldData: any) => {
        if (!oldData?.users) return oldData;
        return {
          ...oldData,
          users: oldData.users.map((member: Member) =>
            member._id === userId ? { ...member, isOnline } : member
          )
        };
      });
    });

    return () => {
      socket.off('userStatusChange');
    };
  }, [socket, queryClient]);

  const toggleExpand = (memberId: string) => {
    setExpandedMember(expandedMember === memberId ? null : memberId);
  };

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Member Directory</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-500">
            Error loading members. Please try again later.
          </div>
        </CardContent>
      </Card>
    );
  }

  // Count members by role (excluding company admins)
  const visibleMembers = allMembers.filter((m: Member) => m.role !== 'company_admin');
  const memberCounts = {
    all: visibleMembers.length,
    admin: visibleMembers.filter((m: Member) => m.role === 'admin').length,
    resident: visibleMembers.filter((m: Member) => m.role === 'member').length,
    property_manager: visibleMembers.filter((m: Member) => m.role === 'property_manager').length,
    online: visibleMembers.filter((m: Member) => m.isOnline).length
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isRegularUser ? 'Street Directory' : 'Member Directory'}</CardTitle>
        <CardDescription>
          {isRegularUser
            ? `${filteredMembers.length} neighbors on your street`
            : `${filteredMembers.length} members found`
          }
          {membersData?.restrictedView && (
            <span className="text-blue-600 ml-2">(Street view only)</span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Search and filter controls */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search members..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full sm:w-auto">
            <TabsList className={`grid ${isRegularUser ? 'grid-cols-2 sm:grid-cols-3' : 'grid-cols-3 sm:grid-cols-5'}`}>
              <TabsTrigger value="all" className="text-xs">
                {isRegularUser ? 'All Neighbors' : 'All'} ({memberCounts.all})
              </TabsTrigger>
              {!isRegularUser && (
                <TabsTrigger value="admin" className="text-xs">
                  Admins ({memberCounts.admin})
                </TabsTrigger>
              )}
              <TabsTrigger value="resident" className="text-xs">
                {isRegularUser ? 'Residents' : 'Residents'} ({memberCounts.resident})
              </TabsTrigger>
              <TabsTrigger value="online" className="text-xs">
                Online ({memberCounts.online})
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{isRegularUser ? 'Neighbor' : 'Member'}</TableHead>
              <TableHead>Property</TableHead>
              <TableHead>Community</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Join Date</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <>
                <LoadingRow />
                <LoadingRow />
                <LoadingRow />
              </>
            ) : filteredMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  No members found matching your search criteria
                </TableCell>
              </TableRow>
            ) : filteredMembers.map((member: Member) => (
              <React.Fragment key={member._id}>
                <TableRow className="border-b-0">
                  <TableCell className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={getProfilePhotoUrl(member.profilePhoto)} />
                      <AvatarFallback>
                        {member.username?.charAt(0) || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{member.username}</div>
                      <div className="text-sm text-muted-foreground">{member.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>{member.propertyAddress || 'No address provided'}</TableCell>
                  <TableCell>
                    {(() => {
                      // Debug logging
                      console.log('Member community data:', {
                        memberId: member._id,
                        memberEmail: member.email,
                        communityId: member.communityId,
                        communityType: typeof member.communityId
                      });

                      if (!member.communityId) {
                        return <span className="text-muted-foreground text-sm">No community</span>;
                      }

                      // Handle string ID (not populated)
                      if (typeof member.communityId === 'string') {
                        return <span className="text-muted-foreground text-sm">Community ID: {member.communityId}</span>;
                      }

                      // Handle array of communities (multiple communities)
                      if (Array.isArray(member.communityId)) {
                        return (
                          <div className="space-y-1">
                            {member.communityId.map((community, index) => (
                              <div key={community._id || index}>
                                <div className="font-medium text-sm">{community.name}</div>
                                {community.streetAddress && (
                                  <div className="text-xs text-muted-foreground">{community.streetAddress}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        );
                      }

                      // Handle single community object
                      return (
                        <div>
                          <div className="font-medium text-sm">{member.communityId.name}</div>
                          {member.communityId.streetAddress && (
                            <div className="text-xs text-muted-foreground">{member.communityId.streetAddress}</div>
                          )}
                        </div>
                      );
                    })()}
                  </TableCell>
                  <TableCell>
                    <RoleBadge role={member.role} size="sm" />
                  </TableCell>
                  <TableCell>
                    {member.isOnline ? (
                      <Badge variant="success">Online</Badge>
                    ) : (
                      <Badge variant="destructive">Offline</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {member.createdAt ? new Date(member.createdAt).toLocaleDateString() : 'Unknown'}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpand(member._id)}
                      className="hover:bg-transparent"
                    >
                      {expandedMember === member._id ?
                        <ChevronUp className="h-4 w-4" /> :
                        <ChevronDown className="h-4 w-4" />
                      }
                    </Button>
                  </TableCell>
                </TableRow>
                {expandedMember === member._id && (
                  <TableRow className="bg-muted/50">
                    <TableCell colSpan={6}>
                      <div className="py-4 px-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-3">
                            <div className="flex items-center gap-2 text-sm">
                              <UserCircle className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">Full Name:</span>
                              <span>{member.fullName}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">Email:</span>
                              <span>{member.email}</span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="flex items-center gap-2 text-sm">
                              <Home className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">Property:</span>
                              <span>{member.propertyAddress || 'No address provided'}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">Member since:</span>
                              <span>{member.createdAt ? new Date(member.createdAt).toLocaleDateString() : 'Unknown'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default MemberDetails;