import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, AlertTriangle, CreditCard } from 'lucide-react';
import { format, set } from 'date-fns';
import api from '@/lib/axios';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useNavigate } from 'react-router-dom';

// Load Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Subscription tier information
const TIERS = {
  basic: {
    name: 'Basic',
    description: 'For small HOAs with up to 50 units',
    price: 150,
    features: [
      'All core features',
      'Up to 50 units',
      'Basic support',
      'Document management',
      'Task management'
    ]
  },
  standard: {
    name: 'Standard',
    description: 'For medium HOAs with up to 150 units',
    price: 350,
    features: [
      'All Basic features',
      'Up to 150 units',
      'Priority support',
      'Advanced reporting',
      'Community management'
    ]
  },
  premium: {
    name: 'Premium',
    description: 'For large HOAs with up to 300 units',
    price: 600,
    features: [
      'All Standard features',
      'Up to 300 units',
      'Premium support',
      'Advanced analytics',
      'Custom branding'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    description: 'For very large HOAs with unlimited units',
    price: 800,
    features: [
      'All Premium features',
      'Unlimited units',
      'Dedicated support',
      'Custom integrations',
      'White-label solution'
    ]
  }
};

type SubscriptionResponse = {
  data,
  hoaId: string;
};

// Tier selection component
const TierSelector = ({ selectedTier, onSelectTier, isLoading }) => {
  return (
    <>
      <div className="mb-4 bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-700">
        <div className="flex items-start">
          <div className="mr-2 mt-0.5">
            <CheckCircle className="h-5 w-5 text-blue-500" />
          </div>
          <div>
            <p className="font-medium">30-Day Free Trial with All Plans</p>
            <p className="text-sm mt-1">
              All subscription plans include a 30-day free trial. You won't be charged until the trial period ends.
              You can cancel anytime during the trial period.
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Object.entries(TIERS).map(([key, tier]) => (
          <Card
            key={key}
            className={`cursor-pointer transition-all ${selectedTier === key ? 'ring-2 ring-primary' : 'hover:shadow-md'}`}
            onClick={() => onSelectTier(key)}
          >
            <CardHeader>
              <CardTitle>{tier.name}</CardTitle>
              <CardDescription>{tier.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center mb-2">
                <p className="text-2xl font-bold">${tier.price}<span className="text-sm font-normal">/month</span></p>
                <Badge variant="outline" className="ml-2 text-blue-500 border-blue-200">30-day free</Badge>
              </div>
              <ul className="mt-4 space-y-2">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Button
                variant={selectedTier === key ? "default" : "outline"}
                className="w-full"
                disabled={isLoading}
              >
                {selectedTier === key ? 'Selected' : 'Select'}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </>
  );
};

// Payment form component
const PaymentForm = ({ hoaId, tier, unitCount, onSuccess, onCancel, existingSubscription }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create subscription mutation (for new subscriptions)
  const createSubscriptionMutation = useMutation<SubscriptionResponse, Error, {
    hoaId: string;
    tier: string;
    unitCount: number;
    paymentMethodId: string;
  }>({
    mutationFn: async ({ hoaId, tier, unitCount, paymentMethodId }) => {
      const response = await api.post('/api/subscriptions', {
        hoaId,
        tier,
        unitCount,
        paymentMethodId
      });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['subscription', hoaId] });
      onSuccess(data);
    },
    onError: (error) => {
      console.error('Error creating subscription:', error);
      setError('Failed to create subscription');
      setIsLoading(false);
    }
  });

  // Update subscription mutation (for existing subscriptions)
  const updateSubscriptionMutation = useMutation<SubscriptionResponse, Error, {
    subscriptionId: string;
    tier: string;
    unitCount: number;
    paymentMethodId?: string;
  }>({
    mutationFn: async ({ subscriptionId, tier, unitCount, paymentMethodId }) => {
      const response = await api.put(`/api/subscriptions/${subscriptionId}`, {
        tier,
        unitCount,
        ...(paymentMethodId && { paymentMethodId })
      });
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['subscription', hoaId] });
      onSuccess(data);
    },
    onError: (error) => {
      console.error('Error updating subscription:', error);
      setError('Failed to update subscription');
      setIsLoading(false);
    }
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const cardElement = elements.getElement(CardElement);

      if (existingSubscription) {
        // For existing subscriptions, we might not need a new payment method
        // if they're in trial period or just changing plan details
        if (existingSubscription.status === 'trialing') {
          // Trial users don't need new payment method for plan changes
          await updateSubscriptionMutation.mutateAsync({
            subscriptionId: existingSubscription._id,
            tier,
            unitCount
          });
        } else {
          // Active subscribers might need new payment method for plan changes
          const { error, paymentMethod } = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
          });

          if (error) {
            throw new Error(error.message);
          }

          await updateSubscriptionMutation.mutateAsync({
            subscriptionId: existingSubscription._id,
            tier,
            unitCount,
            paymentMethodId: paymentMethod.id
          });
        }
      } else {
        // Create new subscription
        const { error, paymentMethod } = await stripe.createPaymentMethod({
          type: 'card',
          card: cardElement,
        });

        if (error) {
          throw new Error(error.message);
        }

        await createSubscriptionMutation.mutateAsync({
          hoaId,
          tier,
          unitCount,
          paymentMethodId: paymentMethod.id
        });
      }

    } catch (err) {
      console.error('Payment error:', err);
      setError(err.message);
      toast({
        title: existingSubscription ? 'Update Failed' : 'Payment Failed',
        description: err.message,
        variant: 'destructive'
      });
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Card Details</label>
          <div className="border rounded-md p-3">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
              }}
            />
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
            <AlertTriangle className="h-5 w-5 mr-2 mt-0.5" />
            <span>{error}</span>
          </div>
        )}

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Back
          </Button>
          <Button
            type="submit"
            disabled={!stripe || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {existingSubscription ? 'Updating...' : 'Processing...'}
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {existingSubscription ? 'Update Subscription' : 'Subscribe Now'}
              </>
            )}
          </Button>
        </div>
      </div>
    </form>
  );
};

// Main subscription management component
const SubscriptionManagement = ({ hoaId }) => {

  const navigate = useNavigate();
  const [selectedTier, setSelectedTier] = useState('basic');
  const [unitCount, setUnitCount] = useState(50);
  const [step, setStep] = useState('select'); // 'select', 'payment', 'success', 'cancel'
  const { toast } = useToast();

  // State to track if we should show the community selection message
  const [showCommunitySelectionMessage, setShowCommunitySelectionMessage] = useState(false);

  // State to track community change loading
  const [isLoadingCommunityChange, setIsLoadingCommunityChange] = useState(false);

  // Community selection state (same as other components)
  const [selectedCommunityId, setSelectedCommunityId] = useState<string | null>(() => {
    const storedId = localStorage.getItem('selectedCommunityId');
    return storedId && storedId !== 'null' ? storedId : null;
  });
  const [selectedCommunityIds, setSelectedCommunityIds] = useState<string[]>(() => {
    const stored = localStorage.getItem('selectedCommunityIds');
    return stored ? JSON.parse(stored) : [];
  });
  const [selectedCommunityName, setSelectedCommunityName] = useState<string | null>(null);
  const [isMultipleCommunities, setIsMultipleCommunities] = useState<boolean>(() => {
    // Initialize from localStorage immediately - check both single and multiple community selection
    try {
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');
      const storedCommunityId = localStorage.getItem('selectedCommunityId');

      console.log('SubscriptionManagement: Initial state check:', {
        storedCommunityIds,
        storedCommunityId
      });

      if (storedCommunityIds) {
        const ids = JSON.parse(storedCommunityIds);
        const isMultiple = Array.isArray(ids) && ids.length > 1;
        console.log('SubscriptionManagement: Multiple communities detected:', isMultiple, ids);
        return isMultiple;
      }

      if (storedCommunityId) {
        console.log('SubscriptionManagement: Single community detected:', storedCommunityId);
        return false; // Single community = not multiple
      }

      console.log('SubscriptionManagement: No community selection detected');
      return false;
    } catch (e) {
      console.log('SubscriptionManagement: Error parsing localStorage:', e);
      return false;
    }
  });
  const [availableCommunitiesInDropdown, setAvailableCommunitiesInDropdown] = useState<string[]>([]);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const userCommunityId = user?.communityId;
  const isCompanyAdmin = user.role === 'company_admin';

  // Listen for community selection changes from Sidebar
  useEffect(() => {
    // Initialize from localStorage
    const storedCommunityId = localStorage.getItem('selectedCommunityId');
    const storedCommunityIds = localStorage.getItem('selectedCommunityIds');



    // Priority: Single community selection takes precedence over multiple
    if (storedCommunityId && storedCommunityId !== 'null' && storedCommunityId !== 'all') {
      setSelectedCommunityId(storedCommunityId);
      setIsMultipleCommunities(false);
      setSelectedCommunityName('Single Community Selected'); // Will be updated by API call
    } else if (storedCommunityIds && storedCommunityIds !== 'null' && storedCommunityIds !== '[]') {
      const ids = JSON.parse(storedCommunityIds);
      setSelectedCommunityIds(ids);
      setIsMultipleCommunities(true);
      setSelectedCommunityName(`${ids.length} Communities Selected`);
    } else {
      // No community selection - reset to default state
      setSelectedCommunityId(null);
      setSelectedCommunityIds([]);
      setIsMultipleCommunities(false);
      setSelectedCommunityName(null);
    }

    const handleCommunityChange = async (event: CustomEvent) => {
      const { communityId, communityIds, availableCommunities } = event.detail;

      // Store available communities from dropdown for "All Communities" use
      if (availableCommunities && Array.isArray(availableCommunities)) {
        const communityIdsList = availableCommunities.map((community: any) => community._id || community.id);
        setAvailableCommunitiesInDropdown(communityIdsList);
      }

      // Show loading state when community changes
      setIsLoadingCommunityChange(true);
      setTimeout(() => setIsLoadingCommunityChange(false), 800);

      // Handle both single and multiple community selection
      if (communityIds && Array.isArray(communityIds)) {
        // Multiple communities selected
        setSelectedCommunityIds(communityIds);
        setSelectedCommunityId(null);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`${communityIds.length} Communities Selected`);

        console.log('SubscriptionManagement: Multiple communities selected:', communityIds);
      } else if (communityId && communityId !== 'all') {
        // Single community selected
        setSelectedCommunityId(communityId);
        setSelectedCommunityIds([]);
        setIsMultipleCommunities(false);

        // Fetch community details for display name
        try {
          const response = await api.get(`/api/communities/${communityId}`);
          const community = response.data;
          if (community) {
            setSelectedCommunityName(`${community.name} (${community.streetAddress || 'No address'})`);
          }
        } catch (error) {
          console.error('Error fetching community details:', error);
          setSelectedCommunityName('Selected Community');
        }
      } else {
        // "All Communities" selection
        setSelectedCommunityId(null);
        const communitiesToUse = communityIds && communityIds.length > 0 ? communityIds : availableCommunitiesInDropdown;
        setSelectedCommunityIds(communitiesToUse);
        setIsMultipleCommunities(true);
        setSelectedCommunityName(`All Communities (${communitiesToUse.length})`);
      }
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange as EventListener);

    return () => {
      window.removeEventListener('communitySelectionChanged', handleCommunityChange as EventListener);
    };
  }, [availableCommunitiesInDropdown]);

  // Additional effect to ensure proper state initialization on component mount
  useEffect(() => {
    // Double-check community state after component mounts
    const checkCommunityState = () => {
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');
      const storedCommunityId = localStorage.getItem('selectedCommunityId');

      if (storedCommunityIds) {
        const ids = JSON.parse(storedCommunityIds);
        if (ids.length > 1) {
          console.log('SubscriptionManagement: Confirmed multiple communities state:', ids);
          setIsMultipleCommunities(true);
          setSelectedCommunityIds(ids);
          setSelectedCommunityId(null);
        }
      } else if (storedCommunityId) {
        console.log('SubscriptionManagement: Confirmed single community state:', storedCommunityId);
        setIsMultipleCommunities(false);
        setSelectedCommunityId(storedCommunityId);
        setSelectedCommunityIds([]);
      }
    };

    // Check immediately and also after a short delay to ensure localStorage is read
    checkCommunityState();
    const timeoutId = setTimeout(checkCommunityState, 100);
    return () => clearTimeout(timeoutId);
  }, []); // Run only on mount

  // Check localStorage directly for multiple communities (used for queries and render)
  const checkMultipleCommunitiesForRender = () => {
    try {
      const storedIds = localStorage.getItem('selectedCommunityIds');
      if (storedIds) {
        const ids = JSON.parse(storedIds);
        return Array.isArray(ids) && ids.length > 1;
      }
      return false;
    } catch {
      return false;
    }
  };

  const hasMultipleCommunitiesForRender = checkMultipleCommunitiesForRender();

  // Determine which HOA ID to use for API calls
  const effectiveHoaId = selectedCommunityId ? null : hoaId; // Use null when community is selected to trigger community-based fetch

  // Simplified query logic - since "All Communities" is handled in SubscriptionPage
  // Force queries to be enabled to debug the issue
  const hoaQueryEnabled = true;



  // Fetch HOA details
  const { data: hoa, isLoading: isLoadingHoa, error: hoaError, refetch: refetchHoa } = useQuery({
    queryKey: ['hoa', effectiveHoaId, selectedCommunityId],
    queryFn: async () => {
      try {
        if (selectedCommunityId) {
          // Fetch HOA data based on selected community
          const response = await api.get(`/api/hoa/by-community/${selectedCommunityId}`);
          return response.data;
        } else {
          // Use original hoaId
          const response = await api.get(`/api/hoa/${hoaId}`);
          return response.data;
        }
      } catch (error) {
        if (error.response?.status === 400) {
          setShowCommunitySelectionMessage(true);
          return null; // Return null instead of throwing to prevent error state
        }
        throw error;
      }
    },
    enabled: hoaQueryEnabled,
    retry: false, // Don't retry on 400 errors
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (formerly cacheTime)
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    refetchOnReconnect: false // Don't refetch on network reconnect
  });

  // Debug the subscription query enabled conditions
  // Force queries to be enabled to debug the issue
  const subscriptionQueryEnabled = true; // !!(effectiveHoaId || selectedCommunityId);



  // Fetch subscription details
  const { data: subscription, isLoading: isLoadingSubscription, error: subscriptionError } = useQuery({
    queryKey: ['subscription', effectiveHoaId, selectedCommunityId],
    queryFn: async () => {
      try {
        if (selectedCommunityId) {
          // Fetch subscription data based on selected community
          const response = await api.get(`/api/subscriptions/by-community/${selectedCommunityId}`);
          return response.data;
        } else {
          // Use original hoaId
          const response = await api.get(`/api/subscriptions/hoa/${hoaId}`);
          return response.data;
        }
      } catch (error) {
        if (error.response?.status === 400) {
          setShowCommunitySelectionMessage(true);
          return null; // Return null instead of throwing to prevent error state
        } else if (error.response?.status === 404) {
          return null; // No subscription found
        }
        throw error;
      }
    },
    enabled: subscriptionQueryEnabled,
    retry: false, // Don't retry on 400 errors
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    refetchOnReconnect: false // Don't refetch on network reconnect
  });

  // Cancel subscription mutation
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (subscriptionId: string) => {
      const response = await api.delete(`/api/subscription/${subscriptionId}`);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate subscription queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      queryClient.invalidateQueries({ queryKey: ['hoa'] });

      toast({
        title: 'Subscription Cancelled',
        description: 'Your subscription has been cancelled successfully.',
        variant: 'default'
      });

      setStep('view');
    },
    onError: (error) => {
      console.error('Cancel subscription failed:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Set initial tier based on HOA data
  useEffect(() => {
    if (hoa?.subscription?.tier) {
      setSelectedTier(hoa.subscription.tier);
    }
    if (hoa?.subscription?.unitCount) {
      setUnitCount(hoa.subscription.unitCount);
    }

    if (subscription?.subscription) {
      setStep('view');
    }
  }, [hoa, subscription]);

  // Force queries to run when component mounts or localStorage changes
  useEffect(() => {
    const currentCommunityId = localStorage.getItem('selectedCommunityId');
    if (currentCommunityId && currentCommunityId !== selectedCommunityId) {
      setSelectedCommunityId(currentCommunityId);
      // Queries will automatically refetch due to dependency change
    } else if (hoaQueryEnabled) {
      refetchHoa();
    }
  }, [selectedCommunityId, hoaQueryEnabled, refetchHoa]);

  const handleSelectTier = (tier) => {
    setSelectedTier(tier);
  };

  const handleProceedToPayment = () => {
    setStep('payment');
  };

  const handleActiveSubscription = () => {
    // Set the current subscription tier as the default selection
    if (subscription?.subscription?.tier) {
      setSelectedTier(subscription.subscription.tier);
    }
    setStep('select');
  };

  const sendCancelSubscription = async () => {
    if (!subscription?.subscription?._id) {
      toast({
        title: 'Error',
        description: 'No subscription found to cancel',
        variant: 'destructive'
      });
      return;
    }

    try {
      await cancelSubscriptionMutation.mutateAsync(subscription.subscription._id);
    } catch (error) {
      // Error handling is done in the mutation's onError callback
      console.error('Cancel subscription failed:', error);
    }
  };

  const handlePaymentSuccess = () => {
    setStep('success');
    toast({
      title: 'Subscription Created',
      description: `Your ${selectedTier} subscription has been created successfully.`,
      variant: 'default'
    });
  };

  const handleCancel = () => {
    setStep('view');
  };

  const handleCancelSubscription = () => {
    setStep('cancel')
  }

  const isLoading = isLoadingHoa || isLoadingSubscription || cancelSubscriptionMutation.isPending;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // If subscription exists, show subscription details
  if (step === 'view' && subscription?.subscription) {
    const sub = subscription.subscription;
    const stripeDetails = subscription.stripeDetails;

    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Current Subscription</CardTitle>
              <CardDescription>
                {hoa?.hoaCommunityName || 'Your HOA'} is currently on the {TIERS[sub.tier]?.name || sub.tier} plan
              </CardDescription>
            </div>
            <Badge
              variant={
                sub.status === 'active' ? 'default' :
                sub.status === 'trialing' ? 'secondary' :
                'destructive'
              }
            >
              {sub.status === 'active' ? 'Active' :
               sub.status === 'trialing' ? 'Free Trial' :
               sub.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Subscription Details</h3>
                <div className="mt-2 space-y-2">
                  <p><span className="font-medium">Plan:</span> {TIERS[sub.tier]?.name || sub.tier}</p>
                  <p><span className="font-medium">Price:</span> ${sub.totalPrice}/month</p>
                  <p><span className="font-medium">Units:</span> {sub.unitCount}</p>
                  <p><span className="font-medium">Status:</span> {sub.status}</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Billing Period</h3>
                <div className="mt-2 space-y-2">
                  <p>
                    <span className="font-medium">Current period:</span>{' '}
                    {stripeDetails?.currentPeriodStart && (
                      <>
                        {format(new Date(stripeDetails.currentPeriodStart), 'MMM d, yyyy')} to{' '}
                        {format(new Date(stripeDetails.currentPeriodEnd), 'MMM d, yyyy')}
                      </>
                    )}
                  </p>
                  {sub.status === 'trialing' && (
                    <p className="text-blue-600 font-medium mt-2">
                      Your free trial ends on {format(new Date(sub.metadata.trialEnd), 'MMMM d, yyyy')}
                    </p>
                  )}
                  {stripeDetails?.cancelAtPeriodEnd && (
                    <p className="text-amber-600 mt-2">
                      Your subscription will be canceled at the end of the current billing period.
                    </p>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Payment Method</h3>
              {stripeDetails?.defaultPaymentMethod ? (
                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  <span>
                    {stripeDetails.defaultPaymentMethod.card.brand.toUpperCase()} ending in{' '}
                    {stripeDetails.defaultPaymentMethod.card.last4}
                  </span>
                </div>
              ) : (
                <p>No payment method on file</p>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex flex-col">
            <Button variant="outline" onClick={() => handleActiveSubscription()}>
              {sub.status === 'trialing' ? 'Update Plan (Free)' : 'Change Plan'}
            </Button>
            {sub.status === 'trialing' && (
              <div className="text-xs text-muted-foreground mt-1">
                Update your plan anytime during the free trial
              </div>
            )}
          </div>
          {sub.status === 'active' && !stripeDetails?.cancelAtPeriodEnd && (
            <Button variant="destructive" onClick={handleCancelSubscription}>
              Cancel Subscription
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  }

  if (step === 'cancel') {
    const stripeDetails = subscription.stripeDetails;
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-center items-center">
            <div>
              <CardTitle>Cancel Subscription</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-4 bg-red-50 border border-red-200 rounded-md">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-red-900 mb-2">
                  Are you sure you want to cancel your subscription?
                </h3>
                <div className="text-sm text-red-800 space-y-1">
                  <p>• Your subscription will remain active until {format(new Date(stripeDetails.currentPeriodEnd), 'MMM d, yyyy')}</p>
                  <p>• After that date, you'll lose access to all HOA management features</p>
                  <p>• Your data will be preserved, but you'll need to resubscribe to access it</p>
                  <p>• You can contact support to reactivate your subscription at any time</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex row justify-center">
          <Button variant="outline" onClick={handleCancel}>
            Keep Subscription
          </Button>
          <div className="col ml-4">
            <Button
              variant="destructive"
              onClick={sendCancelSubscription}
              disabled={cancelSubscriptionMutation.isPending}
            >
              {cancelSubscriptionMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Cancelling...
                </>
              ) : (
                'Yes, Cancel Subscription'
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // Show loading overlay when queries are loading or community is changing
  if (isLoadingHoa || isLoadingSubscription || isLoadingCommunityChange) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>
            Loading subscription data...
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-12">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">
              {isLoadingHoa && isLoadingSubscription
                ? 'Loading HOA and subscription data...'
                : isLoadingHoa
                ? 'Loading HOA information...'
                : 'Loading subscription details...'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Management</CardTitle>
        <CardDescription>
          Choose a subscription plan for {hoa?.hoaCommunityName || 'your HOA'}
          {selectedCommunityName && (
            <div className="text-sm text-muted-foreground mt-1">
              Managing subscription for: {selectedCommunityName}
            </div>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {step === 'select' && (
          <>
            {/* Show current subscription info if updating */}
            {subscription?.subscription && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="text-sm font-medium text-blue-900 mb-2">Current Subscription</h3>
                <div className="text-sm text-blue-800">
                  <p><span className="font-medium">Plan:</span> {TIERS[subscription.subscription.tier]?.name || subscription.subscription.tier}</p>
                  <p><span className="font-medium">Price:</span> ${subscription.subscription.totalPrice}/month</p>
                  <p><span className="font-medium">Status:</span> {subscription.subscription.status === 'trialing' ? 'Free Trial' : 'Active'}</p>
                  {subscription.subscription.status === 'trialing' && (
                    <p className="text-blue-600 font-medium mt-1">
                      Trial ends: {format(new Date(subscription.subscription.metadata.trialEnd), 'MMM d, yyyy')}
                    </p>
                  )}
                </div>
              </div>
            )}

            <TierSelector
              selectedTier={selectedTier}
              onSelectTier={handleSelectTier}
              isLoading={isLoading}
            />

            {/* Show billing impact if changing plans */}
            {subscription?.subscription && selectedTier !== subscription.subscription.tier && (
              <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-md">
                <h3 className="text-sm font-medium text-amber-900 mb-2">Plan Change Impact</h3>
                <div className="text-sm text-amber-800">
                  {subscription.subscription.status === 'trialing' ? (
                    <p>
                      <CheckCircle className="inline h-4 w-4 mr-1 text-green-500" />
                      You can change your plan during the free trial without any additional charges.
                      Your trial period will continue with the new plan.
                    </p>
                  ) : (
                    <div>
                      <p className="mb-2">
                        <AlertTriangle className="inline h-4 w-4 mr-1 text-amber-500" />
                        Changing from <span className="font-medium">{TIERS[subscription.subscription.tier]?.name}</span> to{' '}
                        <span className="font-medium">{TIERS[selectedTier]?.name}</span>
                      </p>
                      <p>
                        • The change will take effect immediately<br/>
                        • You'll be charged a prorated amount for the new plan<br/>
                        • Your next billing date remains the same
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            <div className="row flex mt-4">
              <div className="col">
                <Button variant="destructive" onClick={handleCancel}>
                  Cancel
                </Button>
              </div>
              <div className="col ml-4">
                <Button
                  onClick={handleProceedToPayment}
                  disabled={subscription?.subscription && selectedTier === subscription.subscription.tier}
                >
                  {subscription?.subscription
                    ? (selectedTier === subscription.subscription.tier ? 'No Changes' : 'Update Plan')
                    : 'Continue to Payment'
                  }
                </Button>
              </div>
            </div>
          </>
        )}

        {step === 'payment' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">
                {subscription?.subscription ? 'Update Subscription' : 'Payment Details'}
              </h3>
              <p className="text-muted-foreground">
                {subscription?.subscription
                  ? `Updating to the ${TIERS[selectedTier].name} plan at $${TIERS[selectedTier].price}/month`
                  : `You're subscribing to the ${TIERS[selectedTier].name} plan at $${TIERS[selectedTier].price}/month`
                }
              </p>

              {/* Show different info based on subscription status */}
              {subscription?.subscription ? (
                // Existing subscription update
                <div className="mt-2">
                  {subscription.subscription.status === 'trialing' ? (
                    <div className="bg-green-50 border border-green-200 rounded-md p-3 text-green-700 flex items-start">
                      <div className="mr-2 mt-0.5">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </div>
                      <div>
                        <p className="font-medium">Free Trial Update</p>
                        <p className="text-sm mt-1">
                          You're updating your plan during the free trial period. No additional charges will apply.
                          Your trial will continue with the new plan until {format(new Date(subscription.subscription.metadata.trialEnd), 'MMM d, yyyy')}.
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-amber-50 border border-amber-200 rounded-md p-3 text-amber-700 flex items-start">
                      <div className="mr-2 mt-0.5">
                        <AlertTriangle className="h-5 w-5 text-amber-500" />
                      </div>
                      <div>
                        <p className="font-medium">Plan Update</p>
                        <p className="text-sm mt-1">
                          Your plan will be updated immediately. You'll be charged a prorated amount for the difference.
                          Your next billing date remains {format(new Date(subscription.stripeDetails.currentPeriodEnd), 'MMM d, yyyy')}.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // New subscription
                <div className="mt-2 bg-blue-50 border border-blue-200 rounded-md p-3 text-blue-700 flex items-start">
                  <div className="mr-2 mt-0.5">
                    <CheckCircle className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="font-medium">30-Day Free Trial</p>
                    <p className="text-sm mt-1">
                      Your subscription includes a 30-day free trial. You won't be charged until the trial period ends.
                      You can cancel anytime during the trial period.
                    </p>
                  </div>
                </div>
              )}
            </div>

            <Elements stripe={stripePromise}>
              <PaymentForm
                hoaId={hoaId}
                tier={selectedTier}
                unitCount={unitCount}
                onSuccess={handlePaymentSuccess}
                onCancel={handleCancel}
                existingSubscription={subscription?.subscription}
              />
            </Elements>
          </div>
        )}

        {step === 'success' && (
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-medium mb-2">
              {subscription?.subscription ? 'Subscription Updated Successfully!' : 'Subscription Created Successfully!'}
            </h3>
            <p className="text-muted-foreground mb-2">
              {subscription?.subscription
                ? `Your subscription has been updated to the ${TIERS[selectedTier].name} plan.`
                : `Your ${TIERS[selectedTier].name} subscription is now active with a 30-day free trial.`
              }
            </p>
            {!subscription?.subscription && (
              <p className="text-blue-600 text-sm mb-6">
                Your free trial starts today. You won't be charged until the trial period ends.
              </p>
            )}
            <Button onClick={() => window.location.reload()}>
              View Subscription Details
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SubscriptionManagement;
