import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Building, CreditCard, FileText } from 'lucide-react';
import SubscriptionManagement from '@/components/SubscriptionManagement';
import BillingHistory from '@/components/BillingHistory';
import api from '@/lib/axios';

const SubscriptionPage = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const hoaId  = user?.hoaId || null;
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('subscription');

  // Redirect if no HOA ID
  useEffect(() => {
    if (!hoaId) {
      // Redirect to HOA selection page
      navigate('/admin/hoa-office');
    }
  }, [hoaId, navigate]);

  // Use the HOA ID from the URL
  const effectiveHoaId = hoaId;

  // Check for "All Communities" selection - show message immediately without API calls or tabs
  const checkAllCommunitiesSelection = () => {


    try {
      // First check if there's a single community selected
      const storedCommunityId = localStorage.getItem('selectedCommunityId');
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');



      // If there's a single community ID selected, this is NOT "All Communities"
      if (storedCommunityId && storedCommunityId !== 'null' && storedCommunityId !== 'all' && storedCommunityId !== 'ALL_COMMUNITIES') {
        return false; // Single community selection - show normal interface
      }

      // Check selectedCommunityIds for multiple communities
      if (storedCommunityIds && storedCommunityIds !== 'null' && storedCommunityIds !== '[]') {
        const ids = JSON.parse(storedCommunityIds);
        if (Array.isArray(ids) && ids.length > 1) {
          return true; // Multiple communities - show message
        }
      }

      // Check selectedCommunityId for "all" value
      if (storedCommunityId === 'all' || storedCommunityId === 'ALL_COMMUNITIES') {
        return true;
      }

      // Check for any other patterns that might indicate "All Communities"
      const communitySelection = localStorage.getItem('communitySelection');
      if (communitySelection === 'all' || communitySelection === 'ALL_COMMUNITIES') {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  };

  const isAllCommunitiesSelected = checkAllCommunitiesSelection();

  // Track community changes for loading state
  const [isLoadingCommunityChange, setIsLoadingCommunityChange] = useState(false);

  // Listen for community selection changes to show loading state
  useEffect(() => {
    const handleCommunityChange = () => {
      setIsLoadingCommunityChange(true);
      // Clear loading state after a short delay to allow queries to start
      setTimeout(() => setIsLoadingCommunityChange(false), 500);
    };

    window.addEventListener('communitySelectionChanged', handleCommunityChange);
    return () => window.removeEventListener('communitySelectionChanged', handleCommunityChange);
  }, []);

  // Fetch HOA details only if not "All Communities" selected
  const { data: hoa, isLoading } = useQuery({
    queryKey: ['hoa', effectiveHoaId],
    queryFn: async () => {
      const response = await api.get(`/api/hoa/${effectiveHoaId}`);
      return response.data;
    },
    enabled: !!effectiveHoaId && !isAllCommunitiesSelected, // Enable query only when we have hoaId and not "All Communities"
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    refetchOnReconnect: false // Don't refetch on network reconnect
  });

  // Show friendly message immediately if "All Communities" is selected
  if (isAllCommunitiesSelected) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Building className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">Subscription Management</h1>
          </div>
          <p className="text-muted-foreground">
            Community selection required
          </p>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <CreditCard className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Please select community to show subscription</h3>
                <p className="text-sm text-gray-500 mt-2 max-w-md">
                  To view and manage subscription details, please select a specific community from the sidebar.
                  Subscription management is available on a per-community basis.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!effectiveHoaId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>No HOA Selected</CardTitle>
            <CardDescription>
              Please select an HOA to manage subscriptions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/admin/hoa-office')}>
              Go to HOA Office
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show loading spinner when HOA data is loading or community is changing
  if (isLoading || isLoadingCommunityChange) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Building className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">Subscription Management</h1>
          </div>
          <p className="text-muted-foreground">
            {isLoadingCommunityChange ? 'Loading subscription data...' : 'Loading HOA information...'}
          </p>
        </div>
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Building className="h-6 w-6 mr-2" />
          <h1 className="text-2xl font-bold">
            {hoa?.hoaCommunityName || 'HOA'} Subscription
          </h1>
        </div>
        <p className="text-muted-foreground">
          Manage your HOA subscription, billing, and payment methods
        </p>
      </div>

      <Tabs defaultValue="subscription" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="subscription">
            <CreditCard className="h-4 w-4 mr-2" />
            Subscription
          </TabsTrigger>
          <TabsTrigger value="billing">
            <FileText className="h-4 w-4 mr-2" />
            Billing History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="subscription">
          <div className="relative">
            <SubscriptionManagement hoaId={effectiveHoaId} />
          </div>
        </TabsContent>

        <TabsContent value="billing">
          <div className="relative">
            <BillingHistory hoaId={effectiveHoaId} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubscriptionPage;
