import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Building, CreditCard, FileText } from 'lucide-react';
import SubscriptionManagement from '@/components/SubscriptionManagement';
import BillingHistory from '@/components/BillingHistory';
import api from '@/lib/axios';

const SubscriptionPage = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const hoaId  = user?.hoaId || null;
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('subscription');

  // Redirect if no HOA ID
  useEffect(() => {
    if (!hoaId) {
      // Redirect to HOA selection page
      navigate('/admin/hoa-office');
    }
  }, [hoaId, navigate]);

  // Use the HOA ID from the URL
  const effectiveHoaId = hoaId;

  // Check for "All Communities" selection - show message immediately without API calls or tabs
  const checkAllCommunitiesSelection = () => {
    // Debug: Log all localStorage keys to see what's actually stored
    const allKeys = Object.keys(localStorage);
    console.log('SubscriptionPage: All localStorage keys:', allKeys);

    // Check all community-related keys
    const communityKeys = allKeys.filter(key =>
      key.toLowerCase().includes('community') ||
      key.toLowerCase().includes('selected')
    );

    console.log('SubscriptionPage: Community-related keys:', communityKeys);

    // Log values of all community-related keys
    communityKeys.forEach(key => {
      const value = localStorage.getItem(key);
      console.log(`SubscriptionPage: localStorage['${key}'] =`, value);
    });

    try {
      // First check if there's a single community selected
      const storedCommunityId = localStorage.getItem('selectedCommunityId');
      const storedCommunityIds = localStorage.getItem('selectedCommunityIds');

      console.log('SubscriptionPage: Detailed analysis:', {
        storedCommunityId,
        storedCommunityIds,
        hasSingleId: !!storedCommunityId && storedCommunityId !== 'null',
        hasMultipleIds: storedCommunityIds && storedCommunityIds !== 'null' && storedCommunityIds !== '[]'
      });

      // If there's a single community ID selected, this is NOT "All Communities"
      if (storedCommunityId && storedCommunityId !== 'null' && storedCommunityId !== 'all' && storedCommunityId !== 'ALL_COMMUNITIES') {
        console.log('SubscriptionPage: Single community detected:', storedCommunityId);
        return false; // Single community selection - show normal interface
      }

      // Check selectedCommunityIds for multiple communities
      if (storedCommunityIds && storedCommunityIds !== 'null' && storedCommunityIds !== '[]') {
        const ids = JSON.parse(storedCommunityIds);
        if (Array.isArray(ids) && ids.length > 1) {
          console.log('SubscriptionPage: Multiple communities detected via selectedCommunityIds:', ids);
          return true; // Multiple communities - show message
        }
      }

      // Check selectedCommunityId for "all" value
      if (storedCommunityId === 'all' || storedCommunityId === 'ALL_COMMUNITIES') {
        console.log('SubscriptionPage: All communities detected via selectedCommunityId:', storedCommunityId);
        return true;
      }

      // Check for any other patterns that might indicate "All Communities"
      const communitySelection = localStorage.getItem('communitySelection');
      if (communitySelection === 'all' || communitySelection === 'ALL_COMMUNITIES') {
        console.log('SubscriptionPage: All communities detected via communitySelection:', communitySelection);
        return true;
      }

      console.log('SubscriptionPage: No "All Communities" pattern detected');
      return false;
    } catch (e) {
      console.log('SubscriptionPage: Error checking localStorage:', e);
      return false;
    }
  };

  const isAllCommunitiesSelected = checkAllCommunitiesSelection();

  // Fetch HOA details only if not "All Communities" selected
  const { data: hoa, isLoading } = useQuery({
    queryKey: ['hoa', effectiveHoaId],
    queryFn: async () => {
      const response = await api.get(`/api/hoa/${effectiveHoaId}`);
      return response.data;
    },
    enabled: !!isAllCommunitiesSelected // Disable query if "All Communities" selected
  });

  // Show friendly message immediately if "All Communities" is selected
  if (isAllCommunitiesSelected) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <div className="flex items-center mb-2">
            <Building className="h-6 w-6 mr-2" />
            <h1 className="text-2xl font-bold">Subscription Management</h1>
          </div>
          <p className="text-muted-foreground">
            Community selection required
          </p>
        </div>

        <Card>
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <CreditCard className="w-8 h-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Please select community to show subscription</h3>
                <p className="text-sm text-gray-500 mt-2 max-w-md">
                  To view and manage subscription details, please select a specific community from the sidebar.
                  Subscription management is available on a per-community basis.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!effectiveHoaId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>No HOA Selected</CardTitle>
            <CardDescription>
              Please select an HOA to manage subscriptions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/admin/hoa-office')}>
              Go to HOA Office
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Building className="h-6 w-6 mr-2" />
          <h1 className="text-2xl font-bold">
            {hoa?.hoaCommunityName || 'HOA'} Subscription
          </h1>
        </div>
        <p className="text-muted-foreground">
          Manage your HOA subscription, billing, and payment methods
        </p>
      </div>

      <Tabs defaultValue="subscription" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="subscription">
            <CreditCard className="h-4 w-4 mr-2" />
            Subscription
          </TabsTrigger>
          <TabsTrigger value="billing">
            <FileText className="h-4 w-4 mr-2" />
            Billing History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="subscription">
          <SubscriptionManagement hoaId={effectiveHoaId} />
        </TabsContent>

        <TabsContent value="billing">
          <BillingHistory hoaId={effectiveHoaId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubscriptionPage;
